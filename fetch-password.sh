#!/bin/bash

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Fetch Password
# @raycast.mode 

# Optional parameters:
# @raycast.icon 🔐
# @raycast.description Fetch URL and find the password after "密码："

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou

# Configuration - change these variables as needed
URL="https://nones.xylink.com/wiki/api/wiki/team/AQzvsooq/share/6RQRjTJX/page/5E8iYrdn"
AUTH_TOKEN="9RKKluMTWdKx6shep4wWGZfloeTBV0TZJIBK19exadyEmYyjWIZXKByRSPz0gpgD"  # Replace with your actual auth token
USER_ID="KXymEf3B"        # Replace with your actual user ID
REFERER="https://nones.xylink.com"

# Fetch the content from the URL with authentication
echo "Fetching content from $URL..."
RESPONSE=$(curl -s -X GET \
  "$URL" \
  -H 'Content-Type: application/json' \
  -H "Ones-Auth-Token: $AUTH_TOKEN" \
  -H "Ones-User-Id: $USER_ID" \
  -H "Referer: $REFERER" \
  -H 'cache-control: no-cache')


# Check if curl was successful
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to fetch content from $URL"
    exit 1
fi

# Check if response is empty
if [ -z "$RESPONSE" ]; then
    echo "❌ Error: Empty response from $URL"
    exit 1
fi

echo "✅ Successfully fetched content"

# Extract the content field from JSON and decode HTML entities
CONTENT=$(echo "$RESPONSE" | jq -r '.content' 2>/dev/null)

if [ "$CONTENT" != "null" ] && [ -n "$CONTENT" ]; then
    # Decode unicode escapes and HTML entities
    DECODED_CONTENT=$(echo "$CONTENT" | sed 's/\\u003c/</g; s/\\u003e/>/g; s/\\u0026/\&/g; s/\&nbsp;/ /g; s/\&lt;/</g; s/\&gt;/>/g; s/\&amp;/\&/g')

    # Remove HTML tags to get plain text
    PLAIN_TEXT=$(echo "$DECODED_CONTENT" | sed 's/<[^>]*>//g' | sed 's/\&[^;]*;//g')

    echo "✅ Content extracted and decoded"
else
    echo "❌ No content field found in JSON"
    PLAIN_TEXT="$RESPONSE"
fi

# Look for password after "密码：" in the plain text
PASSWORD=$(echo "$PLAIN_TEXT" | grep -o '密码：[^[:space:]]*' | sed 's/密码：//' | head -1)

# If the above doesn't work, try to capture text after "密码：" until whitespace or newline
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$PLAIN_TEXT" | sed -n 's/.*密码：\([^[:space:]]*\).*/\1/p' | head -1)
fi

# If still empty, try to capture everything after "密码：" until next Chinese character or special delimiter
if [ -z "$PASSWORD" ]; then
    PASSWORD=$(echo "$PLAIN_TEXT" | sed -n 's/.*密码：\([^地址^超管^账号^密码]*\).*/\1/p' | sed 's/[[:space:]]*$//' | head -1)
fi

# Display results
if [ -n "$PASSWORD" ]; then
    echo "🔑 Password found: $PASSWORD"

    # Copy to clipboard
    echo -n "$PASSWORD" | pbcopy
    echo "📋 Password copied to clipboard"
else
    echo "❌ No password found in the response"
    echo ""
    echo "Response content preview:"
    echo "------------------------"
    echo "$RESPONSE" | head -10
    if [ $(echo "$RESPONSE" | wc -l) -gt 10 ]; then
        echo "... (truncated)"
    fi
fi
