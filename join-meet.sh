#!/bin/bash

# Required parameters:
# @raycast.schemaVersion 1
# @raycast.title Join Meet
# @raycast.mode silent

# Optional parameters:
# @raycast.icon 🤖

# Documentation:
# @raycast.author raikyou
# @raycast.authorURL https://raycast.com/raikyou


# Get selected text (requires Accessibility permission)
SELECTED_TEXT=$(osascript -e 'tell application "System Events" to keystroke "c" using command down' -e 'delay 0.1' -e 'get the clipboard')

# Check if selected text exists, otherwise use the provided argument, or default to 9037231696
MEETING_ID="${SELECTED_TEXT:-${1:-9037231696}}"

# Base64 encode the meeting ID
ENCODED_MEETING_ID=$(echo -n "$MEETING_ID" | base64)

# Join meeting
MEETING_URL="https://meet.xylink.com/?number=$ENCODED_MEETING_ID"

open -a "Google Chrome" "$MEETING_URL"

echo "Joining meeting $MEETING_ID"

